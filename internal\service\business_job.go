package service

import (
	"context"
	v1 "daisy-server/api/v1"
	"daisy-server/internal/model"
	"daisy-server/internal/repository"
	"daisy-server/pkg/pagination"
	"time"

	"github.com/gin-gonic/gin"
)

type BusinessJobService interface {
	Create(ctx context.Context, req *v1.BusinessJobCreateParams) error
	Update(ctx context.Context, id uint, req *v1.BusinessJobUpdateParams) error
	Delete(ctx context.Context, id uint) error
	BatchDelete(ctx context.Context, ids []uint) error
	Get(ctx context.Context, id uint) (*v1.BusinessJobResponse, error)
	List(ctx context.Context, params *pagination.Params) (*pagination.Result, error)
}

func NewBusinessJobService(
	service *Service,
	businessJobRepository repository.BusinessJobRepository,
	businessCompanyRepository repository.BusinessCompanyRepository,
) BusinessJobService {
	return &businessJobService{
		Service:                   service,
		businessJobRepository:     businessJobRepository,
		businessCompanyRepository: businessCompanyRepository,
	}
}

type businessJobService struct {
	*Service
	businessJobRepository     repository.BusinessJobRepository
	businessCompanyRepository repository.BusinessCompanyRepository
}

// 招聘相关方法实现
func (s *businessJobService) Create(ctx context.Context, req *v1.BusinessJobCreateParams) error {
	job := &model.BusinessJob{
		CompanyId:  req.CompanyId,
		Title:      req.Title,
		Type:       req.Type,
		SalaryType: req.SalaryType,
		Salary:     req.Salary,
		Tags:       req.Tags,
		Detail:     req.Detail,
		Area:       req.Area,
		Contact:    req.Contact,
		Phone:      req.Phone,
		Email:      req.Email,
		Order:      req.Order,
		Flag:       req.Flag,
		Status:     req.Status,
	}

	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.businessJobRepository.Create(ctx, job); err != nil {
			return err
		}
		return nil
	})
}

func (s *businessJobService) Update(ctx context.Context, id uint, req *v1.BusinessJobUpdateParams) error {
	job, err := s.businessJobRepository.Get(ctx, id)
	if err != nil {
		return err
	}

	// 更新字段
	job.CompanyId = req.CompanyId
	job.Title = req.Title
	job.Type = req.Type
	job.SalaryType = req.SalaryType
	job.Salary = req.Salary
	job.Tags = req.Tags
	job.Detail = req.Detail
	job.Area = req.Area
	job.Contact = req.Contact
	job.Phone = req.Phone
	job.Email = req.Email
	job.Order = req.Order
	job.Flag = req.Flag
	job.Status = req.Status

	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.businessJobRepository.Update(ctx, job); err != nil {
			return err
		}
		return nil
	})
}

func (s *businessJobService) Delete(ctx context.Context, id uint) error {
	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.businessJobRepository.Delete(ctx, id); err != nil {
			return err
		}
		return nil
	})
}

func (s *businessJobService) BatchDelete(ctx context.Context, ids []uint) error {
	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.businessJobRepository.BatchDelete(ctx, ids); err != nil {
			return err
		}
		return nil
	})
}

func (s *businessJobService) Get(ctx context.Context, id uint) (*v1.BusinessJobResponse, error) {
	job, err := s.businessJobRepository.Get(ctx, id)
	if err != nil {
		return nil, err
	}

	return &v1.BusinessJobResponse{
		ID:         job.ID,
		CompanyId:  job.CompanyId,
		Title:      job.Title,
		Type:       job.Type,
		SalaryType: job.SalaryType,
		Salary:     job.Salary,
		Tags:       job.Tags,
		Detail:     job.Detail,
		Area:       job.Area,
		Contact:    job.Contact,
		Phone:      job.Phone,
		Email:      job.Email,
		Order:      job.Order,
		Flag:       job.Flag,
		Status:     job.Status,
		CreatedAt:  job.CreatedAt.Format(time.RFC3339),
		UpdatedAt:  job.UpdatedAt.Format(time.RFC3339),
	}, nil
}

func (s *businessJobService) List(ctx context.Context, params *pagination.Params) (*pagination.Result, error) {
	// 状态筛选
	status := ctx.(*gin.Context).DefaultQuery("status", "")
	if status != "" {
		params.AddFilter("status", status)
	}

	// 标题筛选
	title := ctx.(*gin.Context).DefaultQuery("title", "")
	if title != "" {
		params.AddFilter("title_like", title)
	}

	// 公司ID筛选
	companyId := ctx.(*gin.Context).DefaultQuery("companyId", "")
	if companyId != "" {
		params.AddFilter("company_id", companyId)
	}

	// 岗位类型筛选
	jobType := ctx.(*gin.Context).DefaultQuery("type", "")
	if jobType != "" {
		params.AddFilter("type", jobType)
	}

	// 地区筛选
	area := ctx.(*gin.Context).DefaultQuery("area", "")
	if area != "" {
		params.AddFilter("area_like", area)
	}

	// 标志筛选
	flag := ctx.(*gin.Context).DefaultQuery("flag", "")
	if flag != "" {
		params.AddFilter("flag_any", flag)
	}

	jobs, total, err := s.businessJobRepository.List(ctx, params)
	if err != nil {
		return nil, err
	}

	// 检查是否需要展开公司信息
	expandCompany := ctx.(*gin.Context).Query("_expand") == "company"

	// 将原始记录转换为API响应格式
	records := make([]*v1.BusinessJobResponse, 0)
	for _, job := range jobs {
		jobResponse := &v1.BusinessJobResponse{
			ID:         job.ID,
			CompanyId:  job.CompanyId,
			Title:      job.Title,
			Type:       job.Type,
			SalaryType: job.SalaryType,
			Salary:     job.Salary,
			Tags:       job.Tags,
			Detail:     job.Detail,
			Area:       job.Area,
			Contact:    job.Contact,
			Phone:      job.Phone,
			Email:      job.Email,
			Order:      job.Order,
			Flag:       job.Flag,
			Status:     job.Status,
			CreatedAt:  job.CreatedAt.Format(time.RFC3339),
			UpdatedAt:  job.UpdatedAt.Format(time.RFC3339),
		}

		// 如果需要展开公司信息，则获取公司详情
		if expandCompany && job.CompanyId > 0 {
			company, err := s.businessCompanyRepository.Get(ctx, job.CompanyId)
			if err == nil {
				jobResponse.Company = &v1.BusinessCompanyResponse{
					ID:        company.ID,
					Name:      company.Name,
					Industry:  company.Industry,
					Type:      company.Type,
					Size:      company.Size,
					Cert:      company.Cert,
					Contact:   company.Contact,
					Area:      company.Area,
					Address:   company.Address,
					Phone:     company.Phone,
					Email:     company.Email,
					Website:   company.Website,
					Logo:      company.Logo,
					TianyanId: company.TianyanId,
					Summary:   company.Summary,
					Detail:    company.Detail,
					Albums:    company.Albums,
					Tags:      company.Tags,
					Order:     company.Order,
					Flag:      company.Flag,
					Status:    company.Status,
					CreatedAt: company.CreatedAt.Format(time.RFC3339),
					UpdatedAt: company.UpdatedAt.Format(time.RFC3339),
				}
			}
		}

		records = append(records, jobResponse)
	}

	return &pagination.Result{
		Records: records,
		Total:   total,
	}, nil
}
