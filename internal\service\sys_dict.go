package service

import (
	"context"
	v1 "daisy-server/api/v1"
	"daisy-server/internal/model"
	"daisy-server/internal/repository"
	"daisy-server/pkg/pagination"
	"time"
)

type SysDictService interface {
	Create(ctx context.Context, req *v1.SysDictCreateParams) error
	Update(ctx context.Context, id uint, req *v1.SysDictUpdateParams) error
	Delete(ctx context.Context, id uint) error
	BatchDelete(ctx context.Context, ids []uint) error
	Get(ctx context.Context, id uint) (*v1.SysDictResponse, error)
	List(ctx context.Context, params *pagination.Params) (*pagination.Result, error)
}

func NewSysDictService(
	service *Service,
	sysDictRepository repository.SysDictRepository,
) SysDictService {
	return &sysDictService{
		Service:           service,
		sysDictRepository: sysDictRepository,
	}
}

type sysDictService struct {
	*Service
	sysDictRepository repository.SysDictRepository
}

func (s *sysDictService) Create(ctx context.Context, req *v1.SysDictCreateParams) error {
	dict := &model.SysDict{
		Name:    req.Name,
		Code:    req.Code,
		Summary: req.Summary,
		Status:  req.Status,
		Options: req.Options,
	}
	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.sysDictRepository.Create(ctx, dict); err != nil {
			return err
		}
		return nil
	})
}

func (s *sysDictService) Update(ctx context.Context, id uint, req *v1.SysDictUpdateParams) error {
	dict, err := s.sysDictRepository.Get(ctx, id)
	if err != nil {
		return err
	}

	// 更新字段
	dict.Name = req.Name
	dict.Code = req.Code
	dict.Summary = req.Summary
	dict.Status = req.Status
	dict.Options = req.Options

	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.sysDictRepository.Update(ctx, dict); err != nil {
			return err
		}
		return nil
	})
}

func (s *sysDictService) Delete(ctx context.Context, id uint) error {
	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.sysDictRepository.Delete(ctx, id); err != nil {
			return err
		}
		return nil
	})
}

func (s *sysDictService) BatchDelete(ctx context.Context, ids []uint) error {
	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.sysDictRepository.BatchDelete(ctx, ids); err != nil {
			return err
		}
		return nil
	})
}

func (s *sysDictService) Get(ctx context.Context, id uint) (*v1.SysDictResponse, error) {
	dict, err := s.sysDictRepository.Get(ctx, id)
	if err != nil {
		return nil, err
	}
	return &v1.SysDictResponse{
		ID:        dict.ID,
		Name:      dict.Name,
		Code:      dict.Code,
		Summary:   dict.Summary,
		Status:    dict.Status,
		Options:   dict.Options,
		CreatedAt: dict.CreatedAt.Format(time.RFC3339),
		UpdatedAt: dict.UpdatedAt.Format(time.RFC3339),
	}, nil
}

func (s *sysDictService) List(ctx context.Context, params *pagination.Params) (*pagination.Result, error) {
	dicts, total, err := s.sysDictRepository.List(ctx, params)
	if err != nil {
		return nil, err
	}
	records := make([]*v1.SysDictResponse, 0)
	for _, dict := range dicts {
		records = append(records, &v1.SysDictResponse{
			ID:        dict.ID,
			Name:      dict.Name,
			Code:      dict.Code,
			Summary:   dict.Summary,
			Status:    dict.Status,
			Options:   dict.Options,
			CreatedAt: dict.CreatedAt.Format(time.RFC3339),
			UpdatedAt: dict.UpdatedAt.Format(time.RFC3339),
		})
	}
	return &pagination.Result{
		Records: records,
		Total:   total,
	}, nil
}
