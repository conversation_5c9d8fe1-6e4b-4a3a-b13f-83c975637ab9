package service

import (
	"context"
	v1 "daisy-server/api/v1"
	"daisy-server/internal/model"
	"daisy-server/internal/repository"
	"daisy-server/pkg/pagination"
	"time"
)

type SysConfigService interface {
	Create(ctx context.Context, req *v1.SysConfigCreateParams) error
	Update(ctx context.Context, id uint, req *v1.SysConfigUpdateParams) error
	Delete(ctx context.Context, id uint) error
	BatchDelete(ctx context.Context, ids []uint) error
	Get(ctx context.Context, id uint) (*v1.SysConfigResponse, error)
	List(ctx context.Context, params *pagination.Params) (*pagination.Result, error)
}

func NewSysConfigService(
	service *Service,
	sysConfigRepository repository.SysConfigRepository,
) SysConfigService {
	return &sysConfigService{
		Service:             service,
		sysConfigRepository: sysConfigRepository,
	}
}

type sysConfigService struct {
	*Service
	sysConfigRepository repository.SysConfigRepository
}

// 配置相关方法实现
func (s *sysConfigService) Create(ctx context.Context, req *v1.SysConfigCreateParams) error {
	config := &model.SysConfig{
		Name:    req.Name,
		Code:    req.Code,
		Summary: req.Summary,
		Status:  req.Status,
		Params:  req.Params,
	}

	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.sysConfigRepository.Create(ctx, config); err != nil {
			return err
		}
		return nil
	})
}

func (s *sysConfigService) Update(ctx context.Context, id uint, req *v1.SysConfigUpdateParams) error {
	config, err := s.sysConfigRepository.Get(ctx, id)
	if err != nil {
		return err
	}

	// 更新字段
	config.Name = req.Name
	config.Code = req.Code
	config.Summary = req.Summary
	config.Status = req.Status
	config.Params = req.Params

	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.sysConfigRepository.Update(ctx, config); err != nil {
			return err
		}
		return nil
	})
}

func (s *sysConfigService) Delete(ctx context.Context, id uint) error {
	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.sysConfigRepository.Delete(ctx, id); err != nil {
			return err
		}
		return nil
	})
}

func (s *sysConfigService) BatchDelete(ctx context.Context, ids []uint) error {
	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.sysConfigRepository.BatchDelete(ctx, ids); err != nil {
			return err
		}
		return nil
	})
}

func (s *sysConfigService) Get(ctx context.Context, id uint) (*v1.SysConfigResponse, error) {
	config, err := s.sysConfigRepository.Get(ctx, id)
	if err != nil {
		return nil, err
	}

	return &v1.SysConfigResponse{
		ID:        config.ID,
		Name:      config.Name,
		Code:      config.Code,
		Summary:   config.Summary,
		Status:    config.Status,
		Params:    config.Params,
		CreatedAt: config.CreatedAt.Format(time.RFC3339),
		UpdatedAt: config.UpdatedAt.Format(time.RFC3339),
	}, nil
}

func (s *sysConfigService) List(ctx context.Context, params *pagination.Params) (*pagination.Result, error) {
	configs, total, err := s.sysConfigRepository.List(ctx, params)
	if err != nil {
		return nil, err
	}

	// 将原始记录转换为API响应格式
	records := make([]*v1.SysConfigResponse, 0)
	for _, config := range configs {
		records = append(records, &v1.SysConfigResponse{
			ID:        config.ID,
			Name:      config.Name,
			Code:      config.Code,
			Summary:   config.Summary,
			Status:    config.Status,
			Params:    config.Params,
			CreatedAt: config.CreatedAt.Format(time.RFC3339),
			UpdatedAt: config.UpdatedAt.Format(time.RFC3339),
		})
	}

	return &pagination.Result{
		Records: records,
		Total:   total,
	}, nil
}
