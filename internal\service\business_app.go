package service

import (
	"context"
	v1 "daisy-server/api/v1"
	"daisy-server/internal/model"
	"daisy-server/internal/repository"
	"daisy-server/pkg/pagination"
	"time"

	"github.com/gin-gonic/gin"
)

type BusinessAppService interface {
	Create(ctx context.Context, req *v1.BusinessAppCreateParams) error
	Update(ctx context.Context, id uint, req *v1.BusinessAppUpdateParams) error
	Delete(ctx context.Context, id uint) error
	BatchDelete(ctx context.Context, ids []uint) error
	Get(ctx context.Context, id uint) (*v1.BusinessAppResponse, error)
	List(ctx context.Context, params *pagination.Params) (*pagination.Result, error)
}

func NewBusinessAppService(
	service *Service,
	businessAppRepository repository.BusinessAppRepository,
) BusinessAppService {
	return &businessAppService{
		Service:               service,
		businessAppRepository: businessAppRepository,
	}
}

type businessAppService struct {
	*Service
	businessAppRepository repository.BusinessAppRepository
}

// 应用相关方法实现
func (s *businessAppService) Create(ctx context.Context, req *v1.BusinessAppCreateParams) error {
	app := &model.BusinessApp{
		Name:    req.Name,
		Slug:    req.Slug,
		Logo:    req.Logo,
		Summary: req.Summary,
		Url:     req.Url,
		Tags:    req.Tags,
		Order:   req.Order,
		Status:  req.Status,
	}

	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.businessAppRepository.Create(ctx, app); err != nil {
			return err
		}
		return nil
	})
}

func (s *businessAppService) Update(ctx context.Context, id uint, req *v1.BusinessAppUpdateParams) error {
	app, err := s.businessAppRepository.Get(ctx, id)
	if err != nil {
		return err
	}

	// 更新字段
	app.Name = req.Name
	app.Slug = req.Slug
	app.Logo = req.Logo
	app.Summary = req.Summary
	app.Url = req.Url
	app.Tags = req.Tags
	app.Order = req.Order
	app.Status = req.Status

	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.businessAppRepository.Update(ctx, app); err != nil {
			return err
		}
		return nil
	})
}

func (s *businessAppService) Delete(ctx context.Context, id uint) error {
	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.businessAppRepository.Delete(ctx, id); err != nil {
			return err
		}
		return nil
	})
}

func (s *businessAppService) BatchDelete(ctx context.Context, ids []uint) error {
	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.businessAppRepository.BatchDelete(ctx, ids); err != nil {
			return err
		}
		return nil
	})
}

func (s *businessAppService) Get(ctx context.Context, id uint) (*v1.BusinessAppResponse, error) {
	app, err := s.businessAppRepository.Get(ctx, id)
	if err != nil {
		return nil, err
	}

	return &v1.BusinessAppResponse{
		ID:        app.ID,
		Name:      app.Name,
		Slug:      app.Slug,
		Logo:      app.Logo,
		Summary:   app.Summary,
		Url:       app.Url,
		Tags:      app.Tags,
		Order:     app.Order,
		Status:    app.Status,
		CreatedAt: app.CreatedAt.Format(time.RFC3339),
		UpdatedAt: app.UpdatedAt.Format(time.RFC3339),
	}, nil
}

func (s *businessAppService) List(ctx context.Context, params *pagination.Params) (*pagination.Result, error) {
	// 状态筛选
	status := ctx.(*gin.Context).DefaultQuery("status", "")
	if status != "" {
		params.AddFilter("status", status)
	}

	// 名称筛选
	name := ctx.(*gin.Context).DefaultQuery("name", "")
	if name != "" {
		params.AddFilter("name_like", name)
	}

	// 别名筛选
	slug := ctx.(*gin.Context).DefaultQuery("slug", "")
	if slug != "" {
		params.AddFilter("slug_like", slug)
	}

	apps, total, err := s.businessAppRepository.List(ctx, params)
	if err != nil {
		return nil, err
	}

	// 将原始记录转换为API响应格式
	records := make([]*v1.BusinessAppResponse, 0)
	for _, app := range apps {
		records = append(records, &v1.BusinessAppResponse{
			ID:        app.ID,
			Name:      app.Name,
			Slug:      app.Slug,
			Logo:      app.Logo,
			Summary:   app.Summary,
			Url:       app.Url,
			Tags:      app.Tags,
			Order:     app.Order,
			Status:    app.Status,
			CreatedAt: app.CreatedAt.Format(time.RFC3339),
			UpdatedAt: app.UpdatedAt.Format(time.RFC3339),
		})
	}

	return &pagination.Result{
		Records: records,
		Total:   total,
	}, nil
}
