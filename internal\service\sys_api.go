package service

import (
	"context"
	v1 "daisy-server/api/v1"
	"daisy-server/internal/model"
	"daisy-server/internal/repository"
	"daisy-server/pkg/pagination"
	"encoding/json"
	"os"
	"sort"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"gorm.io/datatypes"
)

type SysApiService interface {
	Create(ctx context.Context, req *v1.SysApiCreateParams) error
	Update(ctx context.Context, id uint, req *v1.SysApiUpdateParams) error
	Delete(ctx context.Context, id uint) error
	BatchDelete(ctx context.Context, ids []uint) error
	Get(ctx context.Context, id uint) (*v1.SysApiResponse, error)
	List(ctx context.Context, params *pagination.Params) (*pagination.Result, error)
	ClearAll(ctx context.Context) error
	BatchCreate(ctx context.Context, apis []v1.SysApiCreateParams) error
	RefreshFromSwagger(ctx context.Context, swaggerPath string) error
}

func NewSysApiService(
	service *Service,
	sysApiRepository repository.SysApiRepository,
) SysApiService {
	return &sysApiService{
		Service:          service,
		sysApiRepository: sysApiRepository,
	}
}

type sysApiService struct {
	*Service
	sysApiRepository repository.SysApiRepository
}

// 接口相关方法实现
func (s *sysApiService) Create(ctx context.Context, req *v1.SysApiCreateParams) error {
	api := &model.SysApi{
		Path:    req.Path,
		Method:  req.Method,
		Tags:    req.Tags,
		Summary: req.Summary,
		Status:  req.Status,
	}
	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.sysApiRepository.Create(ctx, api); err != nil {
			return err
		}
		return nil
	})
}

func (s *sysApiService) Update(ctx context.Context, id uint, req *v1.SysApiUpdateParams) error {
	api, err := s.sysApiRepository.Get(ctx, id)
	if err != nil {
		return err
	}

	// 更新字段
	api.Path = req.Path
	api.Method = req.Method
	api.Tags = req.Tags
	api.Summary = req.Summary
	api.Status = req.Status

	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.sysApiRepository.Update(ctx, api); err != nil {
			return err
		}
		return nil
	})
}

func (s *sysApiService) Delete(ctx context.Context, id uint) error {
	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.sysApiRepository.Delete(ctx, id); err != nil {
			return err
		}
		return nil
	})
}

func (s *sysApiService) BatchDelete(ctx context.Context, ids []uint) error {
	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.sysApiRepository.BatchDelete(ctx, ids); err != nil {
			return err
		}
		return nil
	})
}

func (s *sysApiService) Get(ctx context.Context, id uint) (*v1.SysApiResponse, error) {
	api, err := s.sysApiRepository.Get(ctx, id)
	if err != nil {
		return nil, err
	}
	return &v1.SysApiResponse{
		ID:        api.ID,
		Path:      api.Path,
		Method:    strings.ToUpper(api.Method),
		Tags:      api.Tags,
		Summary:   api.Summary,
		Status:    api.Status,
		CreatedAt: api.CreatedAt.Format(time.RFC3339),
		UpdatedAt: api.UpdatedAt.Format(time.RFC3339),
	}, nil
}

func (s *sysApiService) List(ctx context.Context, params *pagination.Params) (*pagination.Result, error) {
	// 状态筛选
	status := ctx.(*gin.Context).DefaultQuery("status", "")
	if status != "" {
		params.AddFilter("status", status)
	}

	// 路径筛选
	path := ctx.(*gin.Context).DefaultQuery("path", "")
	if path != "" {
		params.AddFilter("path_like", path)
	}

	// 方法筛选
	method := ctx.(*gin.Context).DefaultQuery("method", "")
	if method != "" {
		params.AddFilter("method", method)
	}

	apis, total, err := s.sysApiRepository.List(ctx, params)
	if err != nil {
		return nil, err
	}

	// 将原始记录转换为API响应格式
	records := make([]v1.SysApiResponse, 0)
	for _, api := range apis {
		records = append(records, v1.SysApiResponse{
			ID:        api.ID,
			Path:      api.Path,
			Method:    strings.ToUpper(api.Method),
			Tags:      api.Tags,
			Summary:   api.Summary,
			Status:    api.Status,
			CreatedAt: api.CreatedAt.Format(time.RFC3339),
			UpdatedAt: api.UpdatedAt.Format(time.RFC3339),
		})
	}
	return &pagination.Result{
		Records: records,
		Total:   total,
	}, nil
}

func (s *sysApiService) ClearAll(ctx context.Context) error {
	return s.sysApiRepository.ClearAll(ctx)
}

func (s *sysApiService) BatchCreate(ctx context.Context, apis []v1.SysApiCreateParams) error {
	// 按path字段升序排序
	sort.Slice(apis, func(i, j int) bool {
		return apis[i].Path < apis[j].Path
	})

	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		for _, api := range apis {
			modelApi := &model.SysApi{
				Path:    api.Path,
				Method:  strings.ToUpper(api.Method),
				Tags:    api.Tags,
				Summary: api.Summary,
				Status:  api.Status,
			}
			if err := s.sysApiRepository.Create(ctx, modelApi); err != nil {
				return err
			}
		}
		return nil
	})
}

func (s *sysApiService) RefreshFromSwagger(ctx context.Context, swaggerPath string) error {
	file, err := os.Open(swaggerPath)
	if err != nil {
		return err
	}
	defer file.Close()

	var swagger struct {
		Paths map[string]map[string]struct {
			Summary     string   `json:"summary"`
			Description string   `json:"description"`
			Tags        []string `json:"tags"`
		} `json:"paths"`
	}
	if err := json.NewDecoder(file).Decode(&swagger); err != nil {
		return err
	}

	if err := s.ClearAll(ctx); err != nil {
		return err
	}

	apis := make([]v1.SysApiCreateParams, 0)
	for path, methods := range swagger.Paths {
		for method, info := range methods {
			tagsJson, _ := json.Marshal(info.Tags)
			apis = append(apis, v1.SysApiCreateParams{
				Path:    path,
				Method:  method,
				Tags:    datatypes.JSON(tagsJson),
				Summary: info.Description,
				Status:  true,
			})
		}
	}
	if err := s.BatchCreate(ctx, apis); err != nil {
		return err
	}
	return nil
}
