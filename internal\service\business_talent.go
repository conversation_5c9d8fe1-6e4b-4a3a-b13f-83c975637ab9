package service

import (
	"context"
	v1 "daisy-server/api/v1"
	"daisy-server/internal/model"
	"daisy-server/internal/repository"
	"daisy-server/pkg/pagination"
	"time"

	"github.com/gin-gonic/gin"
)

type BusinessTalentService interface {
	Create(ctx context.Context, req *v1.BusinessTalentCreateParams) error
	Update(ctx context.Context, id uint, req *v1.BusinessTalentUpdateParams) error
	Delete(ctx context.Context, id uint) error
	BatchDelete(ctx context.Context, ids []uint) error
	Get(ctx context.Context, id uint) (*v1.BusinessTalentResponse, error)
	List(ctx context.Context, params *pagination.Params) (*pagination.Result, error)
}

func NewBusinessTalentService(
	service *Service,
	businessTalentRepository repository.BusinessTalentRepository,
) BusinessTalentService {
	return &businessTalentService{
		Service:                  service,
		businessTalentRepository: businessTalentRepository,
	}
}

type businessTalentService struct {
	*Service
	businessTalentRepository repository.BusinessTalentRepository
}

// 人才相关方法实现
func (s *businessTalentService) Create(ctx context.Context, req *v1.BusinessTalentCreateParams) error {
	talent := &model.BusinessTalent{
		Name:    req.Name,
		Gender:  req.Gender,
		Age:     req.Age,
		Edu:     req.Edu,
		Exp:     req.Exp,
		Type:    req.Type,
		Skills:  req.Skills,
		Area:    req.Area,
		Phone:   req.Phone,
		Email:   req.Email,
		Avatar:  req.Avatar,
		Address: req.Address,
		Summary: req.Summary,
		Detail:  req.Detail,
		Party:   req.Party,
		Files:   req.Files,
		Order:   req.Order,
		Flag:    req.Flag,
		Status:  req.Status,
	}

	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.businessTalentRepository.Create(ctx, talent); err != nil {
			return err
		}
		return nil
	})
}

func (s *businessTalentService) Update(ctx context.Context, id uint, req *v1.BusinessTalentUpdateParams) error {
	talent, err := s.businessTalentRepository.Get(ctx, id)
	if err != nil {
		return err
	}

	// 更新字段
	talent.Name = req.Name
	talent.Gender = req.Gender
	talent.Age = req.Age
	talent.Edu = req.Edu
	talent.Exp = req.Exp
	talent.Type = req.Type
	talent.Skills = req.Skills
	talent.Area = req.Area
	talent.Phone = req.Phone
	talent.Email = req.Email
	talent.Avatar = req.Avatar
	talent.Address = req.Address
	talent.Summary = req.Summary
	talent.Detail = req.Detail
	talent.Party = req.Party
	talent.Files = req.Files
	talent.Order = req.Order
	talent.Flag = req.Flag
	talent.Status = req.Status

	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.businessTalentRepository.Update(ctx, talent); err != nil {
			return err
		}
		return nil
	})
}

func (s *businessTalentService) Delete(ctx context.Context, id uint) error {
	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.businessTalentRepository.Delete(ctx, id); err != nil {
			return err
		}
		return nil
	})
}

func (s *businessTalentService) BatchDelete(ctx context.Context, ids []uint) error {
	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.businessTalentRepository.BatchDelete(ctx, ids); err != nil {
			return err
		}
		return nil
	})
}

func (s *businessTalentService) Get(ctx context.Context, id uint) (*v1.BusinessTalentResponse, error) {
	talent, err := s.businessTalentRepository.Get(ctx, id)
	if err != nil {
		return nil, err
	}

	return &v1.BusinessTalentResponse{
		ID:        talent.ID,
		Name:      talent.Name,
		Gender:    talent.Gender,
		Age:       talent.Age,
		Edu:       talent.Edu,
		Exp:       talent.Exp,
		Type:      talent.Type,
		Skills:    talent.Skills,
		Area:      talent.Area,
		Phone:     talent.Phone,
		Email:     talent.Email,
		Avatar:    talent.Avatar,
		Address:   talent.Address,
		Summary:   talent.Summary,
		Detail:    talent.Detail,
		Party:     talent.Party,
		Files:     talent.Files,
		Order:     talent.Order,
		Flag:      talent.Flag,
		Status:    talent.Status,
		CreatedAt: talent.CreatedAt.Format(time.RFC3339),
		UpdatedAt: talent.UpdatedAt.Format(time.RFC3339),
	}, nil
}

func (s *businessTalentService) List(ctx context.Context, params *pagination.Params) (*pagination.Result, error) {
	// 状态筛选
	status := ctx.(*gin.Context).DefaultQuery("status", "")
	if status != "" {
		params.AddFilter("status", status)
	}

	// 姓名筛选
	name := ctx.(*gin.Context).DefaultQuery("name", "")
	if name != "" {
		params.AddFilter("name_like", name)
	}

	// 性别筛选
	gender := ctx.(*gin.Context).DefaultQuery("gender", "")
	if gender != "" {
		params.AddFilter("gender", gender)
	}

	// 人才类型筛选
	talentType := ctx.(*gin.Context).DefaultQuery("type", "")
	if talentType != "" {
		params.AddFilter("type", talentType)
	}

	// 地区筛选
	area := ctx.(*gin.Context).DefaultQuery("area", "")
	if area != "" {
		params.AddFilter("area_like", area)
	}

	// 党员筛选
	party := ctx.(*gin.Context).DefaultQuery("party", "")
	if party != "" {
		params.AddFilter("party", party)
	}

	// 手机筛选
	phone := ctx.(*gin.Context).DefaultQuery("phone", "")
	if phone != "" {
		params.AddFilter("phone", phone)
	}

	// 邮箱筛选
	email := ctx.(*gin.Context).DefaultQuery("email", "")
	if email != "" {
		params.AddFilter("email", email)
	}

	// 标志筛选
	flag := ctx.(*gin.Context).DefaultQuery("flag", "")
	if flag != "" {
		params.AddFilter("flag_any", flag)
	}

	talents, total, err := s.businessTalentRepository.List(ctx, params)
	if err != nil {
		return nil, err
	}

	// 将原始记录转换为API响应格式
	records := make([]*v1.BusinessTalentResponse, 0, len(talents))
	for _, talent := range talents {
		records = append(records, &v1.BusinessTalentResponse{
			ID:        talent.ID,
			Name:      talent.Name,
			Gender:    talent.Gender,
			Age:       talent.Age,
			Edu:       talent.Edu,
			Exp:       talent.Exp,
			Type:      talent.Type,
			Skills:    talent.Skills,
			Area:      talent.Area,
			Phone:     talent.Phone,
			Email:     talent.Email,
			Avatar:    talent.Avatar,
			Address:   talent.Address,
			Summary:   talent.Summary,
			Detail:    talent.Detail,
			Party:     talent.Party,
			Files:     talent.Files,
			Order:     talent.Order,
			Flag:      talent.Flag,
			Status:    talent.Status,
			CreatedAt: talent.CreatedAt.Format(time.RFC3339),
			UpdatedAt: talent.UpdatedAt.Format(time.RFC3339),
		})
	}

	return &pagination.Result{
		Records: records,
		Total:   total,
	}, nil
}
