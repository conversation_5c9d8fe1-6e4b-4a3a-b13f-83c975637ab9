package service

import (
	"context"
	v1 "daisy-server/api/v1"
	"daisy-server/internal/model"
	"daisy-server/internal/repository"
	"daisy-server/pkg/pagination"
	"time"

	"github.com/gin-gonic/gin"
)

type SysMenuService interface {
	Create(ctx context.Context, req *v1.SysMenuCreateParams) error
	Update(ctx context.Context, id uint, req *v1.SysMenuUpdateParams) error
	Delete(ctx context.Context, id uint) error
	BatchDelete(ctx context.Context, ids []uint) error
	Get(ctx context.Context, id uint) (*v1.SysMenuResponse, error)
	List(ctx context.Context, params *pagination.Params) (*pagination.Result, error)
	GetConstantRoutes(ctx context.Context) ([]*model.SysMenu, error)
	GetUserRoutes(ctx context.Context, menuIds []int64, isSuper bool) ([]*model.SysMenu, error)
	CheckRouteExists(ctx context.Context, routeName string) (bool, error)
}

func NewSysMenuService(
	service *Service,
	sysMenuRepository repository.SysMenuRepository,
) SysMenuService {
	return &sysMenuService{
		Service:           service,
		sysMenuRepository: sysMenuRepository,
	}
}

type sysMenuService struct {
	*Service
	sysMenuRepository repository.SysMenuRepository
}

// 菜单相关方法实现
func (s *sysMenuService) Create(ctx context.Context, req *v1.SysMenuCreateParams) error {
	menu := &model.SysMenu{
		MenuType:   req.MenuType,
		MenuName:   req.MenuName,
		RouteName:  req.RouteName,
		RoutePath:  req.RoutePath,
		Layout:     req.Layout,
		Component:  req.Component,
		Icon:       req.Icon,
		Order:      req.Order,
		Href:       req.Href,
		Constant:   req.Constant,
		HideInMenu: req.HideInMenu,
		KeepAlive:  req.KeepAlive,
		MultiTab:   req.MultiTab,
		Status:     req.Status,
		ParentId:   req.ParentId,
	}

	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.sysMenuRepository.Create(ctx, menu); err != nil {
			return err
		}
		return nil
	})
}

func (s *sysMenuService) Update(ctx context.Context, id uint, req *v1.SysMenuUpdateParams) error {
	menu, err := s.sysMenuRepository.Get(ctx, id)
	if err != nil {
		return err
	}

	// 更新字段
	menu.MenuType = req.MenuType
	menu.MenuName = req.MenuName
	menu.RouteName = req.RouteName
	menu.RoutePath = req.RoutePath
	menu.Layout = req.Layout
	menu.Component = req.Component
	menu.Icon = req.Icon
	menu.Order = req.Order
	menu.Href = req.Href
	menu.Constant = req.Constant
	menu.HideInMenu = req.HideInMenu
	menu.KeepAlive = req.KeepAlive
	menu.MultiTab = req.MultiTab
	menu.Status = req.Status
	menu.ParentId = req.ParentId

	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.sysMenuRepository.Update(ctx, menu); err != nil {
			return err
		}
		return nil
	})
}

func (s *sysMenuService) Delete(ctx context.Context, id uint) error {
	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.sysMenuRepository.Delete(ctx, id); err != nil {
			return err
		}
		return nil
	})
}

func (s *sysMenuService) BatchDelete(ctx context.Context, ids []uint) error {
	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.sysMenuRepository.BatchDelete(ctx, ids); err != nil {
			return err
		}
		return nil
	})
}

func (s *sysMenuService) Get(ctx context.Context, id uint) (*v1.SysMenuResponse, error) {
	menu, err := s.sysMenuRepository.Get(ctx, id)
	if err != nil {
		return nil, err
	}

	return &v1.SysMenuResponse{
		ID:         menu.ID,
		MenuType:   menu.MenuType,
		MenuName:   menu.MenuName,
		RouteName:  menu.RouteName,
		RoutePath:  menu.RoutePath,
		Layout:     menu.Layout,
		Component:  menu.Component,
		Icon:       menu.Icon,
		Order:      menu.Order,
		Href:       menu.Href,
		Constant:   menu.Constant,
		HideInMenu: menu.HideInMenu,
		KeepAlive:  menu.KeepAlive,
		MultiTab:   menu.MultiTab,
		Status:     menu.Status,
		ParentId:   menu.ParentId,
		CreatedAt:  menu.CreatedAt.Format(time.RFC3339),
		UpdatedAt:  menu.UpdatedAt.Format(time.RFC3339),
	}, nil
}

func (s *sysMenuService) List(ctx context.Context, params *pagination.Params) (*pagination.Result, error) {
	// 状态筛选
	status := ctx.(*gin.Context).DefaultQuery("status", "")
	if status != "" {
		params.AddFilter("status", status)
	}

	// 常量筛选
	constant := ctx.(*gin.Context).DefaultQuery("constant", "")
	if constant != "" {
		params.AddFilter("constant", constant)
	}

	menus, total, err := s.sysMenuRepository.List(ctx, params)
	if err != nil {
		return nil, err
	}

	// 将原始记录转换为API响应格式
	records := make([]*v1.SysMenuResponse, 0)
	for _, menu := range menus {
		records = append(records, &v1.SysMenuResponse{
			ID:         menu.ID,
			MenuType:   menu.MenuType,
			MenuName:   menu.MenuName,
			RouteName:  menu.RouteName,
			RoutePath:  menu.RoutePath,
			Layout:     menu.Layout,
			Component:  menu.Component,
			Icon:       menu.Icon,
			Order:      menu.Order,
			Href:       menu.Href,
			Constant:   menu.Constant,
			HideInMenu: menu.HideInMenu,
			KeepAlive:  menu.KeepAlive,
			MultiTab:   menu.MultiTab,
			Status:     menu.Status,
			ParentId:   menu.ParentId,
			CreatedAt:  menu.CreatedAt.Format(time.RFC3339),
			UpdatedAt:  menu.UpdatedAt.Format(time.RFC3339),
		})
	}

	return &pagination.Result{
		Records: records,
		Total:   total,
	}, nil
}

func (s *sysMenuService) GetConstantRoutes(ctx context.Context) ([]*model.SysMenu, error) {
	return s.sysMenuRepository.GetConstantRoutes(ctx)
}

func (s *sysMenuService) GetUserRoutes(ctx context.Context, menuIds []int64, isSuper bool) ([]*model.SysMenu, error) {
	return s.sysMenuRepository.GetUserRoutes(ctx, menuIds, isSuper)
}

func (s *sysMenuService) CheckRouteExists(ctx context.Context, routeName string) (bool, error) {
	return s.sysMenuRepository.CheckRouteExists(ctx, routeName)
}
