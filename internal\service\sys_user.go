package service

import (
	"context"
	v1 "daisy-server/api/v1"
	"daisy-server/internal/model"
	"daisy-server/internal/repository"
	"daisy-server/pkg/pagination"
	"time"

	"github.com/gin-gonic/gin"
	"golang.org/x/crypto/bcrypt"
)

type SysUserService interface {
	Create(ctx context.Context, req *v1.SysUserCreateParams) error
	Update(ctx context.Context, id uint, req *v1.SysUserUpdateParams) error
	Delete(ctx context.Context, id uint) error
	BatchDelete(ctx context.Context, ids []uint) error
	Get(ctx context.Context, id uint) (*v1.SysUserResponse, error)
	List(ctx context.Context, params *pagination.Params) (*pagination.Result, error)
}

func NewSysUserService(
	service *Service,
	sysUserRepository repository.SysUserRepository,
) SysUserService {
	return &sysUserService{
		Service:           service,
		sysUserRepository: sysUserRepository,
	}
}

type sysUserService struct {
	*Service
	sysUserRepository repository.SysUserRepository
}

// 用户相关方法实现
func (s *sysUserService) Create(ctx context.Context, req *v1.SysUserCreateParams) error {
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(req.Password), bcrypt.DefaultCost)
	if err != nil {
		return err
	}

	// 生成用户ID
	userId, err := s.sid.GenString()
	if err != nil {
		return err
	}

	user := &model.SysUser{
		UserId:   userId,
		Username: req.Username,
		Password: string(hashedPassword),
		Nickname: req.Nickname,
		Gender:   req.Gender,
		Phone:    req.Phone,
		Email:    req.Email,
		Avatar:   req.Avatar,
		Status:   req.Status,
		RoleIds:  req.RoleIds,
		DeptId:   req.DeptId,
	}

	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.sysUserRepository.Create(ctx, user); err != nil {
			return err
		}
		return nil
	})
}

func (s *sysUserService) Update(ctx context.Context, id uint, req *v1.SysUserUpdateParams) error {
	user, err := s.sysUserRepository.Get(ctx, id)
	if err != nil {
		return err
	}

	// 更新字段
	user.Username = req.Username
	user.Nickname = req.Nickname
	user.Gender = req.Gender
	user.Phone = req.Phone
	user.Email = req.Email
	user.Avatar = req.Avatar
	user.RoleIds = req.RoleIds
	user.Status = req.Status
	user.DeptId = req.DeptId

	// 修改密码
	if req.Password != "" {
		hashedPassword, err := bcrypt.GenerateFromPassword([]byte(req.Password), bcrypt.DefaultCost)
		if err != nil {
			return err
		}
		user.Password = string(hashedPassword)
	}

	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.sysUserRepository.Update(ctx, user); err != nil {
			return err
		}
		return nil
	})
}

func (s *sysUserService) Delete(ctx context.Context, id uint) error {
	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.sysUserRepository.Delete(ctx, id); err != nil {
			return err
		}
		return nil
	})
}

func (s *sysUserService) BatchDelete(ctx context.Context, ids []uint) error {
	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.sysUserRepository.BatchDelete(ctx, ids); err != nil {
			return err
		}
		return nil
	})
}

func (s *sysUserService) Get(ctx context.Context, id uint) (*v1.SysUserResponse, error) {
	user, err := s.sysUserRepository.Get(ctx, id)
	if err != nil {
		return nil, err
	}

	return &v1.SysUserResponse{
		ID:        user.ID,
		UserId:    user.UserId,
		Username:  user.Username,
		Nickname:  user.Nickname,
		Gender:    user.Gender,
		Phone:     user.Phone,
		Email:     user.Email,
		Avatar:    user.Avatar,
		Status:    user.Status,
		RoleIds:   user.RoleIds,
		DeptId:    user.DeptId,
		CreatedAt: user.CreatedAt.Format(time.RFC3339),
		UpdatedAt: user.UpdatedAt.Format(time.RFC3339),
	}, nil
}

func (s *sysUserService) List(ctx context.Context, params *pagination.Params) (*pagination.Result, error) {
	// 全文索引
	q := ctx.(*gin.Context).DefaultQuery("q", "")
	if q != "" {
		params.Query = q
	}

	// 状态筛选
	status := ctx.(*gin.Context).DefaultQuery("status", "")
	if status != "" {
		params.AddFilter("status", status)
	}

	// 用户名筛选
	username := ctx.(*gin.Context).DefaultQuery("username", "")
	if username != "" {
		params.AddFilter("username_like", username)
	}

	// 昵称筛选
	nickname := ctx.(*gin.Context).DefaultQuery("nickname", "")
	if nickname != "" {
		params.AddFilter("nickname_like", nickname)
	}

	// 手机号筛选
	phone := ctx.(*gin.Context).DefaultQuery("phone", "")
	if phone != "" {
		params.AddFilter("phone", phone)
	}

	// 邮箱筛选
	email := ctx.(*gin.Context).DefaultQuery("email", "")
	if email != "" {
		params.AddFilter("email", email)
	}

	// 部门筛选
	deptId := ctx.(*gin.Context).DefaultQuery("deptId", "")
	if deptId != "" {
		params.AddFilter("dept_id", deptId)
	}

	users, total, err := s.sysUserRepository.List(ctx, params)
	if err != nil {
		return nil, err
	}

	// 将原始记录转换为API响应格式
	records := make([]*v1.SysUserResponse, 0)
	for _, user := range users {
		records = append(records, &v1.SysUserResponse{
			ID:        user.ID,
			UserId:    user.UserId,
			Username:  user.Username,
			Nickname:  user.Nickname,
			Gender:    user.Gender,
			Phone:     user.Phone,
			Email:     user.Email,
			Avatar:    user.Avatar,
			Status:    user.Status,
			RoleIds:   user.RoleIds,
			DeptId:    user.DeptId,
			CreatedAt: user.CreatedAt.Format(time.RFC3339),
			UpdatedAt: user.UpdatedAt.Format(time.RFC3339),
		})
	}

	return &pagination.Result{
		Records: records,
		Total:   total,
	}, nil
}
